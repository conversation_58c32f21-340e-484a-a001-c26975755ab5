use wgpu;
use bytemuck::{Pod, Zeroable};

/// Vertex with position, color and normal vector
#[repr(C)]
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, Pod, Zeroable)]
pub struct Vertex {
    pub position: [f32; 3],
    pub color: [f32; 3],
    pub normal: [f32; 3],
}

impl Vertex {
    /// Returns the vertex buffer layout
    pub fn desc() -> wgpu::VertexBufferLayout<'static> {
        wgpu::VertexBufferLayout {
            array_stride: std::mem::size_of::<Vertex>() as wgpu::BufferAddress,
            step_mode: wgpu::VertexStepMode::Vertex,
            attributes: &[
                // Position
                wgpu::VertexAttribute {
                    offset: 0,
                    shader_location: 0,
                    format: wgpu::VertexFormat::Float32x3,
                },
                // Color
                wgpu::VertexAttribute {
                    offset: std::mem::size_of::<[f32; 3]>() as wgpu::BufferAddress,
                    shader_location: 1,
                    format: wgpu::VertexFormat::Float32x3,
                },
                // Normal
                wgpu::VertexAttribute {
                    offset: std::mem::size_of::<[f32; 3]>() as wgpu::BufferAddress * 2,
                    shader_location: 2,
                    format: wgpu::VertexFormat::Float32x3,
                },
            ],
        }
    }
}

/// Creates a cube mesh with proper normals and consistent winding order
pub fn create_cube() -> (Vec<Vertex>, Vec<u16>) {
    let vertices = vec![
        // Front face (red) - normal: [0.0, 0.0, 1.0]
        Vertex { position: [-0.5, -0.5, 0.5], color: [1.0, 0.0, 0.0], normal: [0.0, 0.0, 1.0] },
        Vertex { position: [0.5, -0.5, 0.5], color: [1.0, 0.0, 0.0], normal: [0.0, 0.0, 1.0] },
        Vertex { position: [0.5, 0.5, 0.5], color: [1.0, 0.0, 0.0], normal: [0.0, 0.0, 1.0] },
        Vertex { position: [-0.5, 0.5, 0.5], color: [1.0, 0.0, 0.0], normal: [0.0, 0.0, 1.0] },

        // Back face (green) - normal: [0.0, 0.0, -1.0]
        Vertex { position: [-0.5, -0.5, -0.5], color: [0.0, 1.0, 0.0], normal: [0.0, 0.0, -1.0] },
        Vertex { position: [-0.5, 0.5, -0.5], color: [0.0, 1.0, 0.0], normal: [0.0, 0.0, -1.0] },
        Vertex { position: [0.5, 0.5, -0.5], color: [0.0, 1.0, 0.0], normal: [0.0, 0.0, -1.0] },
        Vertex { position: [0.5, -0.5, -0.5], color: [0.0, 1.0, 0.0], normal: [0.0, 0.0, -1.0] },

        // Top face (blue) - normal: [0.0, 1.0, 0.0]
        Vertex { position: [-0.5, 0.5, -0.5], color: [0.0, 0.0, 1.0], normal: [0.0, 1.0, 0.0] },
        Vertex { position: [-0.5, 0.5, 0.5], color: [0.0, 0.0, 1.0], normal: [0.0, 1.0, 0.0] },
        Vertex { position: [0.5, 0.5, 0.5], color: [0.0, 0.0, 1.0], normal: [0.0, 1.0, 0.0] },
        Vertex { position: [0.5, 0.5, -0.5], color: [0.0, 0.0, 1.0], normal: [0.0, 1.0, 0.0] },

        // Bottom face (yellow) - normal: [0.0, -1.0, 0.0]
        Vertex { position: [-0.5, -0.5, -0.5], color: [1.0, 1.0, 0.0], normal: [0.0, -1.0, 0.0] },
        Vertex { position: [0.5, -0.5, -0.5], color: [1.0, 1.0, 0.0], normal: [0.0, -1.0, 0.0] },
        Vertex { position: [0.5, -0.5, 0.5], color: [1.0, 1.0, 0.0], normal: [0.0, -1.0, 0.0] },
        Vertex { position: [-0.5, -0.5, 0.5], color: [1.0, 1.0, 0.0], normal: [0.0, -1.0, 0.0] },

        // Right face (magenta) - normal: [1.0, 0.0, 0.0]
        Vertex { position: [0.5, -0.5, -0.5], color: [1.0, 0.0, 1.0], normal: [1.0, 0.0, 0.0] },
        Vertex { position: [0.5, 0.5, -0.5], color: [1.0, 0.0, 1.0], normal: [1.0, 0.0, 0.0] },
        Vertex { position: [0.5, 0.5, 0.5], color: [1.0, 0.0, 1.0], normal: [1.0, 0.0, 0.0] },
        Vertex { position: [0.5, -0.5, 0.5], color: [1.0, 0.0, 1.0], normal: [1.0, 0.0, 0.0] },

        // Left face (cyan) - normal: [-1.0, 0.0, 0.0]
        Vertex { position: [-0.5, -0.5, -0.5], color: [0.0, 1.0, 1.0], normal: [-1.0, 0.0, 0.0] },
        Vertex { position: [-0.5, -0.5, 0.5], color: [0.0, 1.0, 1.0], normal: [-1.0, 0.0, 0.0] },
        Vertex { position: [-0.5, 0.5, 0.5], color: [0.0, 1.0, 1.0], normal: [-1.0, 0.0, 0.0] },
        Vertex { position: [-0.5, 0.5, -0.5], color: [0.0, 1.0, 1.0], normal: [-1.0, 0.0, 0.0] },
    ];

    let indices = vec![
        // Front face - CCW when viewed from front (+Z)
        0, 1, 2, 0, 2, 3,
        // Back face - CCW when viewed from back (-Z)
        4, 5, 6, 4, 6, 7,
        // Top face - CCW when viewed from top (+Y)
        8, 9, 10, 8, 10, 11,
        // Bottom face - CCW when viewed from bottom (-Y)
        12, 13, 14, 12, 14, 15,
        // Right face - CCW when viewed from right (+X)
        16, 17, 18, 16, 18, 19,
        // Left face - CCW when viewed from left (-X)
        20, 21, 22, 20, 22, 23,
    ];

    (vertices, indices)
}