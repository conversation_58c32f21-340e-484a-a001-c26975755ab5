use block_mesh::{
    greedy_quads, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MergeVoxel, Voxel, VoxelVisibility, RIGHT_HANDED_Y_UP_CONFIG,
    ndshape::{ConstShape3u32, Shape},
};
use crate::rendering::geometry::Vertex;

/// Represents a voxel type
#[derive(<PERSON><PERSON>, Copy, PartialEq, Eq)]
pub enum VoxelType {
    Empty,
    Solid,
}

impl Voxel for VoxelType {
    fn get_visibility(&self) -> VoxelVisibility {
        match self {
            VoxelType::Empty => VoxelVisibility::Empty,
            VoxelType::Solid => VoxelVisibility::Opaque,
        }
    }
}

impl MergeVoxel for VoxelType {
    type MergeValue = Self;

    fn merge_value(&self) -> Self::MergeValue {
        *self
    }
}

/// A 3D grid of voxels
pub struct VoxelGrid {
    voxels: Vec<VoxelType>,
    size: (usize, usize, usize),
}

impl VoxelGrid {
    /// Creates a new voxel grid with the given dimensions
    pub fn new(width: usize, height: usize, depth: usize) -> Self {
        let size = width * height * depth;
        let mut voxels = Vec::with_capacity(size);
        voxels.resize(size, VoxelType::Empty);

        Self {
            voxels,
            size: (width, height, depth),
        }
    }

    /// Gets the voxel at the given position
    pub fn get(&self, x: usize, y: usize, z: usize) -> Option<VoxelType> {
        if x >= self.size.0 || y >= self.size.1 || z >= self.size.2 {
            return None;
        }

        let index = self.get_index(x, y, z);
        Some(self.voxels[index])
    }

    /// Sets the voxel at the given position
    pub fn set(&mut self, x: usize, y: usize, z: usize, voxel: VoxelType) {
        if x >= self.size.0 || y >= self.size.1 || z >= self.size.2 {
            return;
        }

        let index = self.get_index(x, y, z);
        self.voxels[index] = voxel;
    }

    /// Gets the index for the given position
    fn get_index(&self, x: usize, y: usize, z: usize) -> usize {
        z * self.size.0 * self.size.1 + y * self.size.0 + x
    }

    /// Gets the dimensions of the grid
    pub fn size(&self) -> (usize, usize, usize) {
        self.size
    }

    /// Creates a simple test pattern in the grid
    pub fn create_test_pattern(&mut self) {
        // Create a simple platform
        let (width, height, depth) = self.size;
        let mid_height = height / 2;

        // Create a platform at mid-height
        for z in 0..depth {
            for x in 0..width {
                self.set(x, mid_height, z, VoxelType::Solid);
            }
        }

        // Create some pillars
        for y in mid_height..mid_height + 5 {
            self.set(width / 4, y, depth / 4, VoxelType::Solid);
            self.set(width * 3 / 4, y, depth / 4, VoxelType::Solid);
            self.set(width / 4, y, depth * 3 / 4, VoxelType::Solid);
            self.set(width * 3 / 4, y, depth * 3 / 4, VoxelType::Solid);
        }

        // Create a floating cube
        for z in depth/3..depth*2/3 {
            for y in mid_height + 8..mid_height + 12 {
                for x in width/3..width*2/3 {
                    self.set(x, y, z, VoxelType::Solid);
                }
            }
        }
    }
}

/// Generates a mesh from a voxel grid
pub fn generate_voxel_mesh(grid: &VoxelGrid) -> (Vec<Vertex>, Vec<u16>) {
    let (width, height, depth) = grid.size();

    // Create a buffer for the greedy quads algorithm
    let mut buffer = GreedyQuadsBuffer::new(width * height * depth);

    // Define the shape for the voxel grid
    // Use a shape that can accommodate the maximum possible grid size
    // Since the UI allows sizes up to 32 in each dimension
    type GridShape = ConstShape3u32<32, 32, 32>;

    // Create a voxel array for the algorithm
    // Make sure the array is large enough for the GridShape
    let mut voxel_array = vec![VoxelType::Empty; 32 * 32 * 32];
    for z in 0..depth {
        for y in 0..height {
            for x in 0..width {
                if let Some(voxel) = grid.get(x, y, z) {
                    // Use the GridShape to calculate the index in the fixed-size array
                    let grid_shape = GridShape {};
                    let index = grid_shape.linearize([x as u32, y as u32, z as u32]);
                    voxel_array[index as usize] = voxel;
                }
            }
        }
    }

    // Generate quads using the greedy meshing algorithm
    let faces = RIGHT_HANDED_Y_UP_CONFIG.faces;

    // Make sure the bounds don't exceed the shape's dimensions
    // The maximum valid index is one less than the dimension size (0-based indexing)
    let max_x = width.min(32).min(31) as u32;
    let max_y = height.min(32).min(31) as u32;
    let max_z = depth.min(32).min(31) as u32;

    greedy_quads(
        &voxel_array,
        &GridShape {},
        [0, 0, 0],
        [max_x, max_y, max_z],
        &faces,
        &mut buffer,
    );

    // Convert the quads to vertices and indices
    let mut vertices = Vec::new();
    let mut indices = Vec::new();
    let mut index_counter = 0;

    // Define colors for each face direction
    let face_colors = [
        [1.0, 0.0, 0.0], // Right (+X) - Red
        [0.0, 1.0, 0.0], // Left (-X) - Green
        [0.0, 0.0, 1.0], // Up (+Y) - Blue
        [1.0, 1.0, 0.0], // Down (-Y) - Yellow
        [1.0, 0.0, 1.0], // Front (+Z) - Magenta
        [0.0, 1.0, 1.0], // Back (-Z) - Cyan
    ];

    // Process each face direction
    for (face_idx, direction) in buffer.quads.groups.iter().enumerate() {
        // Get the normal vector for this face direction
        let normal = match face_idx {
            0 => [1.0, 0.0, 0.0],  // +X
            1 => [-1.0, 0.0, 0.0], // -X
            2 => [0.0, 1.0, 0.0],  // +Y
            3 => [0.0, -1.0, 0.0], // -Y
            4 => [0.0, 0.0, 1.0],  // +Z
            5 => [0.0, 0.0, -1.0], // -Z
            _ => [0.0, 0.0, 0.0],  // Should never happen
        };

        let color = face_colors[face_idx];

        // Process each quad in this direction
        for quad in direction.iter() {
            // Calculate the four corners of the quad
            let pos = [
                quad.minimum[0] as f32,
                quad.minimum[1] as f32,
                quad.minimum[2] as f32,
            ];

            // The width, height fields depend on the face direction
            // For example, for a quad on the X face, width is along Z and height is along Y
            let width = quad.width as f32;
            let height = quad.height as f32;

            // Create vertices based on the face direction
            let mut quad_vertices = Vec::with_capacity(4);

            // The width and height are used differently depending on the face direction
            match face_idx {
                0 => { // +X (right face) - width is Z, height is Y
                    let x = pos[0] + 1.0; // One voxel to the right
                    let y_min = pos[1];
                    let y_max = pos[1] + height;
                    let z_min = pos[2];
                    let z_max = pos[2] + width;

                    quad_vertices.push([x, y_min, z_min]); // Bottom-left
                    quad_vertices.push([x, y_max, z_min]); // Top-left
                    quad_vertices.push([x, y_max, z_max]); // Top-right
                    quad_vertices.push([x, y_min, z_max]); // Bottom-right
                },
                1 => { // -X (left face) - width is Z, height is Y
                    let x = pos[0]; // Current position
                    let y_min = pos[1];
                    let y_max = pos[1] + height;
                    let z_min = pos[2];
                    let z_max = pos[2] + width;

                    quad_vertices.push([x, y_min, z_max]); // Bottom-right
                    quad_vertices.push([x, y_max, z_max]); // Top-right
                    quad_vertices.push([x, y_max, z_min]); // Top-left
                    quad_vertices.push([x, y_min, z_min]); // Bottom-left
                },
                2 => { // +Y (top face) - width is X, height is Z
                    let x_min = pos[0];
                    let x_max = pos[0] + width;
                    let y = pos[1] + 1.0; // One voxel up
                    let z_min = pos[2];
                    let z_max = pos[2] + height;

                    quad_vertices.push([x_min, y, z_min]); // Bottom-left
                    quad_vertices.push([x_min, y, z_max]); // Top-left
                    quad_vertices.push([x_max, y, z_max]); // Top-right
                    quad_vertices.push([x_max, y, z_min]); // Bottom-right
                },
                3 => { // -Y (bottom face) - width is X, height is Z
                    let x_min = pos[0];
                    let x_max = pos[0] + width;
                    let y = pos[1]; // Current position
                    let z_min = pos[2];
                    let z_max = pos[2] + height;

                    quad_vertices.push([x_min, y, z_min]); // Bottom-left
                    quad_vertices.push([x_max, y, z_min]); // Bottom-right
                    quad_vertices.push([x_max, y, z_max]); // Top-right
                    quad_vertices.push([x_min, y, z_max]); // Top-left
                },
                4 => { // +Z (front face) - width is X, height is Y
                    let x_min = pos[0];
                    let x_max = pos[0] + width;
                    let y_min = pos[1];
                    let y_max = pos[1] + height;
                    let z = pos[2] + 1.0; // One voxel forward

                    quad_vertices.push([x_max, y_min, z]); // Bottom-right
                    quad_vertices.push([x_max, y_max, z]); // Top-right
                    quad_vertices.push([x_min, y_max, z]); // Top-left
                    quad_vertices.push([x_min, y_min, z]); // Bottom-left
                },
                5 => { // -Z (back face) - width is X, height is Y
                    let x_min = pos[0];
                    let x_max = pos[0] + width;
                    let y_min = pos[1];
                    let y_max = pos[1] + height;
                    let z = pos[2]; // Current position

                    quad_vertices.push([x_min, y_min, z]); // Bottom-left
                    quad_vertices.push([x_min, y_max, z]); // Top-left
                    quad_vertices.push([x_max, y_max, z]); // Top-right
                    quad_vertices.push([x_max, y_min, z]); // Bottom-right
                },
                _ => unreachable!(),
            }

            // Add the vertices
            for pos in quad_vertices {
                vertices.push(Vertex {
                    position: pos,
                    color,
                    normal,
                });
            }

            // Add indices for two triangles (6 indices)
            indices.extend_from_slice(&[
                index_counter,
                index_counter + 1,
                index_counter + 2,
                index_counter,
                index_counter + 2,
                index_counter + 3,
            ]);

            index_counter += 4;
        }
    }

    (vertices, indices)
}

/// Creates a test voxel grid and generates a mesh from it
pub fn create_voxel_test() -> (Vec<Vertex>, Vec<u16>) {
    let mut grid = VoxelGrid::new(32, 32, 32);
    grid.create_test_pattern();
    generate_voxel_mesh(&grid)
}

/// Creates a voxel grid with a single voxel in the center and generates a mesh from it
pub fn create_single_voxel() -> (Vec<Vertex>, Vec<u16>) {
    let mut grid = VoxelGrid::new(32, 32, 32);
    // Place a single voxel in the center of the grid
    let center_x = grid.size().0 / 2;
    let center_y = grid.size().1 / 2;
    let center_z = grid.size().2 / 2;
    grid.set(center_x, center_y, center_z, VoxelType::Solid);
    generate_voxel_mesh(&grid)
}
