use wgpu;
use wgpu::util::DeviceExt;
use glam::Mat4;
use crate::rendering::geometry::Vertex;

/// Creates a camera uniform buffer and bind group
pub fn create_camera_resources(
    device: &wgpu::Device,
    camera_matrix: Mat4,
) -> (wgpu::Buffer, wgpu::BindGroupLayout, wgpu::BindGroup) {
    // Create camera uniform buffer
    let camera_buffer = device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
        label: Some("Camera Buffer"),
        contents: bytemuck::cast_slice(&[camera_matrix.to_cols_array_2d()]),
        usage: wgpu::BufferUsages::UNIFORM | wgpu::BufferUsages::COPY_DST,
    });

    // Create camera bind group layout
    let camera_bind_group_layout = device.create_bind_group_layout(&wgpu::BindGroupLayoutDescriptor {
        label: Some("Camera Bind Group Layout"),
        entries: &[wgpu::BindGroupLayoutEntry {
            binding: 0,
            visibility: wgpu::ShaderStages::VERTEX,
            ty: wgpu::BindingType::Buffer {
                ty: wgpu::BufferBindingType::Uniform,
                has_dynamic_offset: false,
                min_binding_size: None,
            },
            count: None,
        }],
    });

    // Create camera bind group
    let camera_bind_group = device.create_bind_group(&wgpu::BindGroupDescriptor {
        label: Some("Camera Bind Group"),
        layout: &camera_bind_group_layout,
        entries: &[wgpu::BindGroupEntry {
            binding: 0,
            resource: camera_buffer.as_entire_binding(),
        }],
    });

    (camera_buffer, camera_bind_group_layout, camera_bind_group)
}

/// Updates the camera uniform buffer with a new view-projection matrix
pub fn update_camera_buffer(
    queue: &wgpu::Queue,
    camera_buffer: &wgpu::Buffer,
    camera_matrix: Mat4,
) {
    queue.write_buffer(camera_buffer, 0, bytemuck::cast_slice(&[camera_matrix.to_cols_array_2d()]));
}

/// Creates vertex and index buffers for a mesh
pub fn create_mesh_buffers(
    device: &wgpu::Device,
    vertices: &[Vertex],
    indices: &[u16],
) -> (wgpu::Buffer, wgpu::Buffer) {
    let vertex_buffer = device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
        label: Some("Vertex Buffer"),
        contents: bytemuck::cast_slice(vertices),
        usage: wgpu::BufferUsages::VERTEX,
    });

    let index_buffer = device.create_buffer_init(&wgpu::util::BufferInitDescriptor {
        label: Some("Index Buffer"),
        contents: bytemuck::cast_slice(indices),
        usage: wgpu::BufferUsages::INDEX,
    });

    (vertex_buffer, index_buffer)
}