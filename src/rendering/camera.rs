use glam::{Mat4, Vec3, Quat};
use winit::event::{<PERSON>ementState, WindowEvent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DeviceEvent};
use winit::keyboard::{KeyCode, PhysicalKey};
use winit::window::CursorGrabMode;
use std::collections::HashSet;

/// Camera for viewing the 3D scene
pub struct Camera {
    position: Vec3,
    rotation: Quat,
    fov: f32,
    aspect: f32,
    near: f32,
    far: f32,

    // Camera movement speed
    move_speed: f32,
    rotate_speed: f32,
    min_move_speed: f32,
    max_move_speed: f32,
    speed_adjustment_factor: f32,

    // Input state
    pressed_keys: HashSet<KeyCode>,

    // Mouse look
    cursor_locked: bool,
    mouse_sensitivity: f32,

    // Debug mode
    debug_mode: bool,
}

impl Camera {
    /// Creates a new camera
    pub fn new(aspect: f32) -> Self {
        Self {
            position: Vec3::new(0.0, 0.0, 3.0),
            rotation: Quat::IDENTITY,
            fov: 45.0_f32.to_radians(),
            aspect,
            near: 0.1,
            far: 100.0,
            move_speed: 2.0,
            rotate_speed: 1.0,
            min_move_speed: 0.5,
            max_move_speed: 10.0,
            speed_adjustment_factor: 0.5,
            pressed_keys: HashSet::new(),
            cursor_locked: false,
            mouse_sensitivity: 0.3,
            debug_mode: false,
        }
    }

    /// Returns the view matrix for the camera
    pub fn view_matrix(&self) -> Mat4 {
        let forward = self.rotation * -Vec3::Z;
        let up = self.rotation * Vec3::Y;

        Mat4::look_to_rh(self.position, forward, up)
    }

    /// Returns the projection matrix for the camera
    pub fn projection_matrix(&self) -> Mat4 {
        Mat4::perspective_rh(self.fov, self.aspect, self.near, self.far)
    }

    /// Returns the view-projection matrix for the camera
    pub fn view_projection_matrix(&self) -> Mat4 {
        self.projection_matrix() * self.view_matrix()
    }

    /// Updates the camera's aspect ratio
    pub fn update_aspect(&mut self, aspect: f32) {
        self.aspect = aspect;
    }

    /// Toggles debug mode
    pub fn toggle_debug_mode(&mut self) {
        self.debug_mode = !self.debug_mode;

        // Automatically lock cursor when debug mode is enabled
        if self.debug_mode {
            self.cursor_locked = true;
        } else {
            self.cursor_locked = false;
        }
    }

    /// Returns whether debug mode is enabled
    pub fn is_debug_mode(&self) -> bool {
        self.debug_mode
    }

    /// Updates the key state based on keyboard input
    pub fn handle_key_code(&mut self, key_code: KeyCode, state: ElementState, _dt: f32) -> bool {
        if !self.debug_mode {
            return false;
        }

        // Handle ESC key to toggle cursor lock
        if key_code == KeyCode::Escape && state == ElementState::Pressed {
            self.toggle_cursor_lock();
            return true;
        }

        // Update key state
        match state {
            ElementState::Pressed => {
                self.pressed_keys.insert(key_code);
            }
            ElementState::Released => {
                self.pressed_keys.remove(&key_code);
            }
        }

        // Check if this is a key we care about
        match key_code {
            KeyCode::KeyW | KeyCode::KeyS | KeyCode::KeyA | KeyCode::KeyD |
            KeyCode::Space | KeyCode::ShiftLeft |
            KeyCode::ArrowUp | KeyCode::ArrowDown | KeyCode::ArrowLeft | KeyCode::ArrowRight => {
                return true;
            }
            _ => {}
        }

        false
    }

    /// Updates the camera position and rotation based on the current key state
    pub fn update(&mut self, dt: f32) {
        if !self.debug_mode {
            return;
        }

        let move_amount = self.move_speed * dt;
        let rotate_amount = self.rotate_speed * dt;

        // Process movement keys
        let mut movement = Vec3::ZERO;

        if self.pressed_keys.contains(&KeyCode::KeyW) {
            let forward = self.rotation * -Vec3::Z;
            movement += forward;
        }
        if self.pressed_keys.contains(&KeyCode::KeyS) {
            let forward = self.rotation * -Vec3::Z;
            movement -= forward;
        }
        if self.pressed_keys.contains(&KeyCode::KeyA) {
            let right = self.rotation * Vec3::X;
            movement -= right;
        }
        if self.pressed_keys.contains(&KeyCode::KeyD) {
            let right = self.rotation * Vec3::X;
            movement += right;
        }
        if self.pressed_keys.contains(&KeyCode::Space) {
            movement.y += 1.0;
        }
        if self.pressed_keys.contains(&KeyCode::ShiftLeft) {
            movement.y -= 1.0;
        }

        // Normalize movement vector if it's not zero
        if movement != Vec3::ZERO {
            movement = movement.normalize();
            self.position += movement * move_amount;
        }

        // Process rotation keys (for when mouse look is not being used)
        if self.pressed_keys.contains(&KeyCode::ArrowUp) {
            let right = self.rotation * Vec3::X;
            self.rotation = Quat::from_axis_angle(right, rotate_amount) * self.rotation;
        }
        if self.pressed_keys.contains(&KeyCode::ArrowDown) {
            let right = self.rotation * Vec3::X;
            self.rotation = Quat::from_axis_angle(right, -rotate_amount) * self.rotation;
        }
        if self.pressed_keys.contains(&KeyCode::ArrowLeft) {
            let up = Vec3::Y;
            self.rotation = Quat::from_axis_angle(up, rotate_amount) * self.rotation;
        }
        if self.pressed_keys.contains(&KeyCode::ArrowRight) {
            let up = Vec3::Y;
            self.rotation = Quat::from_axis_angle(up, -rotate_amount) * self.rotation;
        }
    }

    /// Adjusts the camera movement speed based on mouse scroll
    pub fn handle_mouse_scroll(&mut self, delta: &MouseScrollDelta) -> bool {
        if !self.debug_mode {
            return false;
        }

        let scroll_amount = match delta {
            MouseScrollDelta::LineDelta(_, y) => *y,
            MouseScrollDelta::PixelDelta(delta) => delta.y as f32 * 0.01, // Scale pixel delta
        };

        // Adjust move speed based on scroll amount
        self.move_speed += scroll_amount * self.speed_adjustment_factor;

        // Clamp to min/max values
        self.move_speed = self.move_speed.clamp(self.min_move_speed, self.max_move_speed);

        true
    }

    /// Toggles the cursor lock state
    pub fn toggle_cursor_lock(&mut self) {
        self.cursor_locked = !self.cursor_locked;
    }

    /// Returns whether the cursor is locked
    pub fn is_cursor_locked(&self) -> bool {
        self.cursor_locked
    }

    /// Handles mouse movement for camera rotation
    pub fn handle_mouse_motion(&mut self, delta_x: f32, delta_y: f32) -> bool {
        if !self.debug_mode || !self.cursor_locked {
            return false;
        }

        // Apply mouse sensitivity
        let dx = -delta_x * self.mouse_sensitivity;
        let dy = delta_y * self.mouse_sensitivity;

        // Rotate around Y axis (yaw)
        let up = Vec3::Y;
        self.rotation = Quat::from_axis_angle(up, dx.to_radians()) * self.rotation;

        // Rotate around local X axis (pitch)
        let right = self.rotation * Vec3::X;

        // Limit pitch to prevent camera flipping
        // Get the current forward vector
        let forward = self.rotation * -Vec3::Z;

        // Calculate the angle between forward and up
        let angle = forward.angle_between(Vec3::Y);

        // Only apply pitch if it won't flip the camera
        if (angle > 0.1 && dy > 0.0) || (angle < std::f32::consts::PI - 0.1 && dy < 0.0) {
            self.rotation = Quat::from_axis_angle(right, -dy.to_radians()) * self.rotation;
        }

        true
    }

    /// Updates the camera based on window events
    pub fn handle_window_event(&mut self, event: &WindowEvent, dt: f32) -> bool {
        match event {
            WindowEvent::KeyboardInput { 
                event: winit::event::KeyEvent {
                    physical_key: PhysicalKey::Code(key_code),
                    state,
                    ..
                },
                ..
            } => self.handle_key_code(*key_code, *state, dt),
            WindowEvent::MouseWheel { delta, .. } => self.handle_mouse_scroll(delta),
            _ => false,
        }
    }

    /// Handles device events for mouse movement
    pub fn handle_device_event(&mut self, event: &DeviceEvent) -> bool {
        match event {
            DeviceEvent::MouseMotion { delta } => {
                self.handle_mouse_motion(delta.0 as f32, delta.1 as f32)
            },
            _ => false,
        }
    }

    /// Sets the cursor grab mode for the window
    pub fn apply_cursor_lock(&self, window: &winit::window::Window) {
        if self.debug_mode {
            let grab_mode = if self.cursor_locked {
                CursorGrabMode::Locked
            } else {
                CursorGrabMode::None
            };

            // Set cursor grab mode
            let _ = window.set_cursor_grab(grab_mode);

            // Show/hide cursor
            window.set_cursor_visible(!self.cursor_locked);
        }
    }
}
