// Shader definitions for the application

// Uniform buffer for camera
pub const CAMERA_SHADER: &str = r#"
    struct CameraUniform {
        view_proj: mat4x4<f32>,
    }

    @group(0) @binding(0)
    var<uniform> camera: CameraUniform;

    struct VertexInput {
        @location(0) position: vec3<f32>,
        @location(1) color: vec3<f32>,
        @location(2) normal: vec3<f32>,
    }

    struct VertexOutput {
        @builtin(position) clip_position: vec4<f32>,
        @location(0) color: vec3<f32>,
        @location(1) normal: vec3<f32>,
    }

    @vertex
    fn vs_main(in: VertexInput) -> VertexOutput {
        var out: VertexOutput;
        out.clip_position = camera.view_proj * vec4<f32>(in.position, 1.0);
        out.color = in.color;
        out.normal = in.normal;
        return out;
    }

    @fragment
    fn fs_main(in: VertexOutput) -> @location(0) vec4<f32> {
        // Calculate basic lighting
        let light_dir = normalize(vec3<f32>(0.5, 0.8, 1.0));
        let normal = normalize(in.normal);
        let diffuse = max(dot(normal, light_dir), 0.0);

        // Mix ambient and diffuse lighting
        let ambient = 0.3;
        let light_color = vec3<f32>(1.0, 1.0, 1.0);
        let lighting = ambient + diffuse * 0.7;

        return vec4<f32>(in.color * lighting, 1.0);
    }
"#;

// Legacy triangle shader (kept for reference)
#[allow(dead_code)]
pub const TRIANGLE_SHADER: &str = r#"
    @vertex
    fn vs_main(@builtin(vertex_index) in_vertex_index: u32) -> @builtin(position) vec4<f32> {
        var positions = array<vec2<f32>, 3>(
            vec2<f32>(0.0, 0.5),    // top center
            vec2<f32>(-0.5, -0.5),  // bottom left
            vec2<f32>(0.5, -0.5)    // bottom right
        );
        return vec4<f32>(positions[in_vertex_index], 0.0, 1.0);
    }

    @fragment
    fn fs_main() -> @location(0) vec4<f32> {
        return vec4<f32>(1.0, 0.0, 0.0, 1.0); // Red color
    }
"#;
