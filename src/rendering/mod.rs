// Rendering module for the application
mod geometry;
pub mod shaders;
mod camera;
mod pipeline;
mod buffers;
pub mod voxels;

// Re-export public items
pub use geometry::create_cube;
pub use camera::Camera;
pub use pipeline::create_render_pipeline;
pub use buffers::{create_camera_resources, update_camera_buffer, create_mesh_buffers};
pub use voxels::{create_voxel_test, generate_voxel_mesh, VoxelGrid, VoxelType};

// Re-export the render function
pub use crate::rendering::renderer::render;

// Private renderer module
mod renderer;
