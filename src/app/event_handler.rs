use winit::event_loop::EventLoop;
use crate::app::App;

/// Runs the application event loop
pub async fn run() {
    // Create the event loop
    let event_loop = EventLoop::new().unwrap();

    // Create our application handler
    let mut app_handler = AppHandler {
        app: None,
    };

    // Run the event loop with our application handler
    let _ = event_loop.run_app(&mut app_handler);
}

/// Application handler for the event loop
struct AppHandler {
    app: Option<App>,
}

impl winit::application::ApplicationHandler for AppHandler {
    fn resumed(&mut self, event_loop: &winit::event_loop::ActiveEventLoop) {
        // Initialize the app if it hasn't been created yet
        if self.app.is_none() {
            self.app = Some(pollster::block_on(App::new(event_loop)));
        }
    }

    fn window_event(
        &mut self,
        event_loop: &winit::event_loop::ActiveEventLoop,
        window_id: winit::window::WindowId,
        event: winit::event::WindowEvent,
    ) {
        // Skip if app isn't initialized yet
        let Some(app_ref) = self.app.as_ref() else { return };

        if window_id == app_ref.window().id() {
            // Get mutable reference for handling events
            let app_mut = self.app.as_mut().unwrap();

            // Let the UI handle the event first
            let event_handled_by_ui = app_mut.handle_window_event(&event);

            // If the UI didn't handle the event, process it ourselves
            if !event_handled_by_ui {
                match event {
                    winit::event::WindowEvent::CloseRequested => event_loop.exit(),
                    winit::event::WindowEvent::Resized(physical_size) => {
                        app_mut.resize(physical_size);
                    }
                    _ => {}
                }
            }

            // Always handle redraw requests
            if let winit::event::WindowEvent::RedrawRequested = event {
                match app_mut.render() {
                    Ok(_) => {}
                    // Reconfigure the surface if it's lost or outdated
                    Err(wgpu::SurfaceError::Lost | wgpu::SurfaceError::Outdated) => {
                        let size = app_mut.window().inner_size();
                        app_mut.resize(size);
                    }
                    // The system is out of memory, we should probably quit
                    Err(wgpu::SurfaceError::OutOfMemory) => event_loop.exit(),
                    // We're ignoring all other errors for now
                    Err(e) => eprintln!("{:?}", e),
                }
            }
        }
    }

    fn device_event(
        &mut self,
        _event_loop: &winit::event_loop::ActiveEventLoop,
        _device_id: winit::event::DeviceId,
        event: winit::event::DeviceEvent,
    ) {
        // Skip if app isn't initialized yet
        let Some(app_mut) = self.app.as_mut() else { return };

        // Pass device events to the camera
        let camera = app_mut.camera_mut();
        camera.handle_device_event(&event);
    }

    fn about_to_wait(&mut self, _event_loop: &winit::event_loop::ActiveEventLoop) {
        // Request a redraw if app is initialized
        if let Some(app_ref) = self.app.as_ref() {
            app_ref.window().request_redraw();
        }
    }
}
