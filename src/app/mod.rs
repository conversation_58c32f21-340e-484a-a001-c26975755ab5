// Application module
mod event_handler;

use std::time::Instant;
use winit::{
    event::WindowEvent,
    window::Window,
};
use wgpu;

use crate::rendering::{Camera, create_camera_resources, create_render_pipeline,
                      create_mesh_buffers, update_camera_buffer, render, create_voxel_test};
use crate::rendering::voxels::create_single_voxel;
use crate::rendering::shaders;
use crate::ui::{AppUi, EguiRenderer};
use crate::world::{WorldGenerator, WorldParameters};

// Re-export the run function
pub use crate::app::event_handler::run;

/// Represents the application state
pub struct App {
    surface: wgpu::Surface<'static>,
    device: wgpu::Device,
    queue: wgpu::Queue,
    config: wgpu::SurfaceConfiguration,
    render_pipeline: wgpu::RenderPipeline,
    window: Window,
    // UI components
    egui_renderer: <PERSON>gui<PERSON><PERSON><PERSON>,
    app_ui: AppUi,
    // Camera
    camera: Camera,
    camera_buffer: wgpu::Buffer,
    camera_bind_group: wgpu::BindGroup,
    // Mesh
    vertex_buffer: wgpu::Buffer,
    index_buffer: wgpu::Buffer,
    num_indices: u32,
    // World
    world_generator: WorldGenerator,
    // Timing
    last_frame_time: Instant,
    delta_time: f32,
}

impl App {
    /// Creates a new application instance
    pub async fn new(event_loop: &winit::event_loop::ActiveEventLoop) -> Self {
        // Create the window
        let window = event_loop.create_window(
            winit::window::WindowAttributes::default()
                .with_title("Voxel Renderer")
                .with_inner_size(winit::dpi::LogicalSize::new(800, 600))
        ).unwrap();

        // Create an instance of wgpu
        let instance = wgpu::Instance::default();

        // Create a surface to draw on
        let surface = unsafe {
            // SAFETY: We're creating a surface for a window that we own
            instance.create_surface_unsafe(
                wgpu::SurfaceTargetUnsafe::from_window(&window).unwrap()
            )
        }.unwrap();

        // Request an adapter (GPU)
        let adapter = instance
            .request_adapter(&wgpu::RequestAdapterOptions {
                power_preference: wgpu::PowerPreference::default(),
                force_fallback_adapter: false,
                compatible_surface: Some(&surface),
            })
            .await
            .unwrap();

        // Request a device and queue
        let (device, queue) = adapter
            .request_device(
                &wgpu::DeviceDescriptor {
                    label: None,
                    required_features: wgpu::Features::empty(),
                    required_limits: wgpu::Limits::default(),
                    memory_hints: wgpu::MemoryHints::default(),
                },
                None, // trace path
            )
            .await
            .unwrap();

        // Configure the surface
        let size = window.inner_size();
        let surface_caps = surface.get_capabilities(&adapter);
        let format = surface_caps
            .formats
            .iter()
            .copied()
            .find(|f| f.is_srgb())
            .unwrap_or(surface_caps.formats[0]);

        let config = wgpu::SurfaceConfiguration {
            usage: wgpu::TextureUsages::RENDER_ATTACHMENT,
            format,
            width: size.width,
            height: size.height,
            present_mode: wgpu::PresentMode::Fifo,
            alpha_mode: surface_caps.alpha_modes[0],
            view_formats: vec![],
            desired_maximum_frame_latency: 2,
        };
        surface.configure(&device, &config);

        // Initialize camera
        let aspect_ratio = config.width as f32 / config.height as f32;
        let camera = Camera::new(aspect_ratio);

        // Create camera resources
        let (camera_buffer, camera_bind_group_layout, camera_bind_group) =
            create_camera_resources(&device, camera.view_projection_matrix());

        // Initialize app UI
        let app_ui = AppUi::new();

        // Initialize world generator with default parameters
        let world_params = app_ui.world_parameters().clone();
        let world_generator = WorldGenerator::new(world_params);

        // Generate voxel mesh from the world
        let grid = world_generator.generate();
        let (vertices, indices) = crate::rendering::generate_voxel_mesh(&grid);
        let (vertex_buffer, index_buffer) =
            create_mesh_buffers(&device, &vertices, &indices);
        let num_indices = indices.len() as u32;

        // Create the render pipeline
        let render_pipeline = create_render_pipeline(
            &device,
            shaders::CAMERA_SHADER,
            config.format,
            &camera_bind_group_layout,
        );

        // Initialize egui renderer
        let scale_factor = window.scale_factor();
        let egui_renderer = EguiRenderer::new(&window, &device, &config, scale_factor);

        // Initialize timing
        let last_frame_time = Instant::now();

        Self {
            surface,
            device,
            queue,
            config,
            render_pipeline,
            window,
            egui_renderer,
            app_ui,
            camera,
            camera_buffer,
            camera_bind_group,
            vertex_buffer,
            index_buffer,
            num_indices,
            world_generator,
            last_frame_time,
            delta_time: 0.0,
        }
    }

    /// Handles window resize events
    pub fn resize(&mut self, new_size: winit::dpi::PhysicalSize<u32>) {
        if new_size.width > 0 && new_size.height > 0 {
            self.config.width = new_size.width;
            self.config.height = new_size.height;
            self.surface.configure(&self.device, &self.config);

            // Update camera aspect ratio
            let aspect_ratio = new_size.width as f32 / new_size.height as f32;
            self.camera.update_aspect(aspect_ratio);

            // Update camera buffer with new projection matrix
            update_camera_buffer(
                &self.queue,
                &self.camera_buffer,
                self.camera.view_projection_matrix()
            );

            // Update egui renderer with new size
            let scale_factor = self.window.scale_factor();
            self.egui_renderer.resize(new_size.width, new_size.height, scale_factor);
        }
    }

    /// Renders a frame
    pub fn render(&mut self) -> Result<(), wgpu::SurfaceError> {
        // Update delta time
        let current_time = Instant::now();
        self.delta_time = current_time.duration_since(self.last_frame_time).as_secs_f32();
        self.last_frame_time = current_time;

        // Get the current output texture
        let output = self.surface.get_current_texture()?;
        let view = output
            .texture
            .create_view(&wgpu::TextureViewDescriptor::default());

        // Create command encoder
        let mut encoder = self.device
            .create_command_encoder(&wgpu::CommandEncoderDescriptor {
                label: Some("Render Encoder"),
            });

        // Begin egui frame and update UI
        let egui_ctx = self.egui_renderer.begin_frame(&self.window);
        self.app_ui.update(egui_ctx);

        // Get background color from UI
        let bg_color = self.app_ui.background_color();

        // Update camera debug mode from UI
        let camera_debug_enabled = self.app_ui.is_camera_debug_enabled();
        if self.camera.is_debug_mode() != camera_debug_enabled {
            self.camera.toggle_debug_mode();
        }

        // Check if we need to regenerate the world
        if self.app_ui.should_regenerate_world() {
            // Get the current world parameters from the UI
            let params = self.app_ui.world_parameters().clone();

            // Update the world generator with the new parameters
            self.world_generator.update_parameters(params);

            // Generate a new voxel grid
            let grid = self.world_generator.generate();

            // Generate a new mesh from the grid
            let (vertices, indices) = crate::rendering::generate_voxel_mesh(&grid);

            // Create new buffers for the mesh
            let (vertex_buffer, index_buffer) = 
                create_mesh_buffers(&self.device, &vertices, &indices);

            // Update the app's buffers
            self.vertex_buffer = vertex_buffer;
            self.index_buffer = index_buffer;
            self.num_indices = indices.len() as u32;

            // Reset the regenerate flag
            self.app_ui.reset_regenerate_flag();
        }

        // Check if we need to generate a single voxel world
        if self.app_ui.should_generate_single_voxel() {
            // Generate a single voxel mesh
            let (vertices, indices) = create_single_voxel();

            // Create new buffers for the mesh
            let (vertex_buffer, index_buffer) = 
                create_mesh_buffers(&self.device, &vertices, &indices);

            // Update the app's buffers
            self.vertex_buffer = vertex_buffer;
            self.index_buffer = index_buffer;
            self.num_indices = indices.len() as u32;

            // Reset the single voxel flag
            self.app_ui.reset_single_voxel_flag();
        }

        // Apply cursor lock state
        self.camera.apply_cursor_lock(&self.window);

        // Update camera position and rotation based on input
        self.camera.update(self.delta_time);

        // Update camera buffer with current view-projection matrix
        update_camera_buffer(
            &self.queue,
            &self.camera_buffer,
            self.camera.view_projection_matrix()
        );

        // Render the 3D scene using the cube mesh and camera
        let render_command_buffer = render(
            &self.device,
            &self.queue,
            &self.render_pipeline,
            &self.vertex_buffer,
            &self.index_buffer,
            self.num_indices,
            &self.camera_bind_group,
            &view,
            self.config.width,
            self.config.height,
            wgpu::Color {
                r: bg_color[0] as f64,
                g: bg_color[1] as f64,
                b: bg_color[2] as f64,
                a: 1.0,
            },
        );

        // Render egui
        self.egui_renderer.end_frame_and_render(
            &self.window,
            &self.device,
            &self.queue,
            &view,
            &mut encoder,
        );

        // Submit both render and UI command buffers
        self.queue.submit([render_command_buffer, encoder.finish()]);
        output.present();

        Ok(())
    }

    /// Returns a reference to the window
    pub fn window(&self) -> &Window {
        &self.window
    }

    /// Handles window events for the UI and camera
    pub fn handle_window_event(&mut self, event: &WindowEvent) -> bool {
        // Let egui handle the event first
        let handled_by_ui = self.egui_renderer.handle_event(&self.window, event);

        // If not handled by UI, let the camera handle it
        if !handled_by_ui {
            if self.camera.handle_window_event(event, self.delta_time) {
                return true;
            }
        }

        handled_by_ui
    }

    /// Returns a mutable reference to the camera
    pub fn camera_mut(&mut self) -> &mut Camera {
        &mut self.camera
    }
}
