use noise::{NoiseFn, <PERSON>lin, Seedable};
use crate::rendering::voxels::{VoxelGrid, VoxelType};
use crate::world::parameters::WorldParameters;

/// Generates voxel worlds using noise functions
pub struct WorldGenerator {
    parameters: WorldParameters,
    noise: Perlin,
}

impl WorldGenerator {
    /// Creates a new world generator with the given parameters
    pub fn new(parameters: WorldParameters) -> Self {
        let noise = Perlin::new(parameters.seed);
        Self { parameters, noise }
    }

    /// Updates the generator with new parameters
    pub fn update_parameters(&mut self, parameters: WorldParameters) {
        self.noise = Perlin::new(parameters.seed);
        self.parameters = parameters;
    }

    /// Generates a voxel grid based on the current parameters
    pub fn generate(&self) -> VoxelGrid {
        let (width, height, depth) = self.parameters.size;
        let mut grid = VoxelGrid::new(width, height, depth);

        // Generate terrain using noise
        for z in 0..depth {
            for x in 0..width {
                // Calculate 2D noise value for this x,z coordinate
                let noise_value = self.get_noise_value(x, z);
                
                // Map noise value to height
                let terrain_height = self.map_to_height(noise_value, height);
                
                // Fill in voxels from bottom to terrain height
                for y in 0..terrain_height {
                    grid.set(x, y, z, VoxelType::Solid);
                }
            }
        }

        // Add some caves using 3D noise
        for z in 0..depth {
            for y in 0..height {
                for x in 0..width {
                    // Only process solid voxels
                    if let Some(VoxelType::Solid) = grid.get(x, y, z) {
                        // Calculate 3D noise value
                        let cave_noise = self.get_3d_noise_value(x, y, z);
                        
                        // If noise value is below threshold, create a cave
                        if cave_noise < -0.7 {
                            grid.set(x, y, z, VoxelType::Empty);
                        }
                    }
                }
            }
        }

        grid
    }

    /// Gets a noise value for the given x,z coordinates
    fn get_noise_value(&self, x: usize, z: usize) -> f64 {
        let scale = self.parameters.noise_scale;
        let octaves = self.parameters.octaves;
        let persistence = self.parameters.persistence;
        let lacunarity = self.parameters.lacunarity;
        
        // Calculate coordinates in noise space
        let nx = x as f64 * scale;
        let nz = z as f64 * scale;
        
        // Calculate fractal Brownian motion (fBm)
        let mut amplitude = 1.0;
        let mut frequency = 1.0;
        let mut noise_value = 0.0;
        let mut max_value = 0.0;
        
        for _ in 0..octaves {
            let sample_x = nx * frequency;
            let sample_z = nz * frequency;
            
            let value = self.noise.get([sample_x, sample_z]);
            noise_value += value * amplitude;
            
            max_value += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        // Normalize the result
        noise_value / max_value
    }
    
    /// Gets a 3D noise value for the given x,y,z coordinates
    fn get_3d_noise_value(&self, x: usize, y: usize, z: usize) -> f64 {
        let scale = self.parameters.noise_scale * 2.0; // Use a different scale for 3D noise
        
        // Calculate coordinates in noise space
        let nx = x as f64 * scale;
        let ny = y as f64 * scale;
        let nz = z as f64 * scale;
        
        // Get the noise value
        self.noise.get([nx, ny, nz])
    }
    
    /// Maps a noise value to a height value
    fn map_to_height(&self, noise_value: f64, max_height: usize) -> usize {
        // Map from [-1, 1] to [0, max_height * 0.8]
        let height_factor = 0.8;
        let normalized = (noise_value + 1.0) / 2.0;
        let height = (normalized * max_height as f64 * height_factor) as usize;
        
        // Ensure height is at least 1 and at most max_height - 1
        height.max(1).min(max_height - 1)
    }
    
    /// Returns the current parameters
    pub fn parameters(&self) -> &WorldParameters {
        &self.parameters
    }
}