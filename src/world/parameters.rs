use std::fmt;

/// Parameters for world generation
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct WorldParameters {
    /// Random seed for world generation
    pub seed: u32,
    /// Size of the world in voxels (width, height, depth)
    pub size: (usize, usize, usize),
    /// Scale of the noise (higher values = more zoomed out)
    pub noise_scale: f64,
    /// Threshold for determining solid vs. empty voxels
    pub threshold: f64,
    /// Octaves for the noise function (more octaves = more detail)
    pub octaves: usize,
    /// Persistence for the noise function (controls amplitude decrease)
    pub persistence: f64,
    /// Lacunarity for the noise function (controls frequency increase)
    pub lacunarity: f64,
}

impl Default for WorldParameters {
    fn default() -> Self {
        Self {
            seed: 42,
            size: (32, 32, 32),
            noise_scale: 0.05,
            threshold: 0.0,
            octaves: 4,
            persistence: 0.5,
            lacunarity: 2.0,
        }
    }
}

impl fmt::Display for WorldParameters {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Seed: {}, Size: {}x{}x{}, Scale: {:.3}, Threshold: {:.2}, Octaves: {}, Persistence: {:.2}, Lacunarity: {:.2}",
               self.seed,
               self.size.0, self.size.1, self.size.2,
               self.noise_scale,
               self.threshold,
               self.octaves,
               self.persistence,
               self.lacunarity)
    }
}
