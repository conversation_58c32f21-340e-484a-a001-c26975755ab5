use egui::Context;
use egui_wgpu::{<PERSON><PERSON><PERSON>, ScreenDescriptor};
use wgpu::{Device, Queue, SurfaceConfiguration, TextureView};
use winit::event::WindowEvent;
use winit::window::Window;

/// <PERSON>les rendering egui UI with wgpu
pub struct EguiRenderer {
    context: Context,
    renderer: Renderer,
    screen_descriptor: ScreenDescriptor,
    state: egui_winit::State,
}

impl EguiRenderer {
    /// Creates a new egui renderer
    pub fn new(
        window: &Window,
        device: &Device,
        surface_config: &SurfaceConfiguration,
        scale_factor: f64,
    ) -> Self {
        // Create egui context
        let context = Context::default();

        // Setup egui winit platform
        let state = egui_winit::State::new(
            context.clone(),
            egui::ViewportId::default(),
            window,
            None, // max_texture_side (None = unlimited)
            None, // theme
            None, // repaint_delay
        );

        // Setup screen descriptor (for correct scaling)
        let screen_descriptor = ScreenDescriptor {
            size_in_pixels: [surface_config.width, surface_config.height],
            pixels_per_point: scale_factor as f32,
        };

        // Create renderer
        let renderer = Renderer::new(
            device,
            surface_config.format,
            None, // No sRGB textures
            1,    // Sample count
            false, // depth testing
        );

        Self {
            context,
            renderer,
            screen_descriptor,
            state,
        }
    }

    /// Handles window events for egui
    pub fn handle_event(&mut self, window: &Window, event: &WindowEvent) -> bool {
        let response = self.state.on_window_event(window, event);
        response.consumed
    }

    /// Resizes the egui renderer
    pub fn resize(&mut self, width: u32, height: u32, scale_factor: f64) {
        self.screen_descriptor = ScreenDescriptor {
            size_in_pixels: [width, height],
            pixels_per_point: scale_factor as f32,
        };
    }

    /// Begins a new frame and returns the context for UI building
    pub fn begin_frame(&mut self, window: &Window) -> &Context {
        // Get input state from winit
        let raw_input = self.state.take_egui_input(window);

        // Begin the frame
        self.context.begin_pass(raw_input);
        &self.context
    }

    /// Ends the frame and renders the UI
    pub fn end_frame_and_render(
        &mut self,
        window: &Window,
        device: &Device,
        queue: &Queue,
        view: &TextureView,
        encoder: &mut wgpu::CommandEncoder,
    ) {
        // End the frame and get the output
        let output = self.context.end_pass();
        let clipped_primitives = self.context.tessellate(output.shapes, output.pixels_per_point);

        // Upload all resources to the GPU
        let tdelta = output.textures_delta;

        // Process texture delta
        for (id, image_delta) in tdelta.set {
            self.renderer.update_texture(device, queue, id, &image_delta);
        }

        // Free textures marked for deletion
        for id in tdelta.free {
            self.renderer.free_texture(&id);
        }

        // Create a render pass for egui
        self.renderer.update_buffers(
            device,
            queue,
            encoder,
            &clipped_primitives,
            &self.screen_descriptor,
        );

        // Create a render pass for egui
        {
            // We need to scope the render_pass so it's dropped before we submit the encoder
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Egui Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Load, // Don't clear, we're drawing on top
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // SAFETY: We're only using the render_pass within this scope,
            // and it will be dropped before we submit the encoder
            unsafe {
                use std::mem::transmute;
                let render_pass: &mut wgpu::RenderPass<'static> = transmute(&mut render_pass);
                self.renderer.render(render_pass, &clipped_primitives, &self.screen_descriptor);
            }
        }

        // Update the state with the output
        self.state.handle_platform_output(window, output.platform_output);
    }

    /// Returns a reference to the egui context
    #[allow(dead_code)]
    pub fn context(&self) -> &Context {
        &self.context
    }
}
