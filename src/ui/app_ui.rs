use egui::Context;
use crate::world::WorldParameters;

/// Represents the application UI
pub struct AppUi {
    // UI state variables
    show_demo_window: bool,
    show_settings: bool,
    show_camera_debug: bool,
    show_world_settings: bool,
    background_color: [f32; 3],
    camera_debug_enabled: bool,

    // World generation parameters
    world_params: WorldParameters,
    regenerate_world: bool,
    generate_single_voxel: bool,
}

impl AppUi {
    /// Creates a new application UI
    pub fn new() -> Self {
        Self {
            show_demo_window: false,
            show_settings: true,
            show_camera_debug: true,
            show_world_settings: true,
            background_color: [0.1, 0.2, 0.3],
            camera_debug_enabled: true,
            world_params: WorldParameters::default(),
            regenerate_world: false,
            generate_single_voxel: false,
        }
    }

    /// Renders the UI for the current frame
    pub fn update(&mut self, ctx: &Context) {
        // Top menu bar
        self.render_menu_bar(ctx);

        // Camera debug window (if enabled)
        if self.show_camera_debug {
            egui::Window::new("Camera Debug")
                .open(&mut self.show_camera_debug)
                .show(ctx, |ui| {
                    ui.heading("Camera Controls");
                    ui.separator();

                    ui.checkbox(&mut self.camera_debug_enabled, "Enable Debug Camera");

                    ui.separator();
                    ui.label("Controls when debug camera is enabled:");
                    ui.label("WASD - Move camera");
                    ui.label("Space/Shift - Move up/down");
                    ui.label("Arrow keys - Rotate camera");
                });
        }

        // World settings window (if enabled)
        if self.show_world_settings {
            egui::Window::new("World Settings")
                .open(&mut self.show_world_settings)
                .show(ctx, |ui| {
                    ui.heading("World Generation Parameters");
                    ui.separator();

                    // Seed input
                    ui.horizontal(|ui| {
                        ui.label("Seed:");
                        let mut seed_str = self.world_params.seed.to_string();
                        if ui.text_edit_singleline(&mut seed_str).changed() {
                            if let Ok(seed) = seed_str.parse::<u32>() {
                                self.world_params.seed = seed;
                            }
                        }
                        if ui.button("Random").clicked() {
                            use std::time::{SystemTime, UNIX_EPOCH};
                            let time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap();
                            self.world_params.seed = time.as_secs() as u32;
                        }
                    });

                    // World size
                    ui.collapsing("World Size", |ui| {
                        let mut size = self.world_params.size;
                        ui.horizontal(|ui| {
                            ui.label("Width:");
                            if ui.add(egui::Slider::new(&mut size.0, 16..=32).step_by(16.0)).changed() {
                                self.world_params.size.0 = size.0;
                            }
                        });
                        ui.horizontal(|ui| {
                            ui.label("Height:");
                            if ui.add(egui::Slider::new(&mut size.1, 16..=32).step_by(16.0)).changed() {
                                self.world_params.size.1 = size.1;
                            }
                        });
                        ui.horizontal(|ui| {
                            ui.label("Depth:");
                            if ui.add(egui::Slider::new(&mut size.2, 16..=32).step_by(16.0)).changed() {
                                self.world_params.size.2 = size.2;
                            }
                        });
                    });

                    // Noise parameters
                    ui.collapsing("Noise Parameters", |ui| {
                        ui.horizontal(|ui| {
                            ui.label("Scale:");
                            if ui.add(egui::Slider::new(&mut self.world_params.noise_scale, 0.01..=0.2).logarithmic(true)).changed() {
                                // Scale changed
                            }
                        });

                        ui.horizontal(|ui| {
                            ui.label("Threshold:");
                            if ui.add(egui::Slider::new(&mut self.world_params.threshold, -0.5..=0.5)).changed() {
                                // Threshold changed
                            }
                        });

                        ui.horizontal(|ui| {
                            ui.label("Octaves:");
                            if ui.add(egui::Slider::new(&mut self.world_params.octaves, 1..=8).step_by(1.0)).changed() {
                                // Octaves changed
                            }
                        });

                        ui.horizontal(|ui| {
                            ui.label("Persistence:");
                            if ui.add(egui::Slider::new(&mut self.world_params.persistence, 0.1..=0.9)).changed() {
                                // Persistence changed
                            }
                        });

                        ui.horizontal(|ui| {
                            ui.label("Lacunarity:");
                            if ui.add(egui::Slider::new(&mut self.world_params.lacunarity, 1.0..=4.0)).changed() {
                                // Lacunarity changed
                            }
                        });
                    });

                    ui.separator();

                    // Generate button
                    if ui.button("Generate World").clicked() {
                        self.regenerate_world = true;
                    }

                    // Display current parameters
                    ui.separator();
                    ui.label(format!("Current parameters: {}", self.world_params));
                });
        }

        // Demo window (if enabled)
        if self.show_demo_window {
            egui::Window::new("Egui Demo")
                .open(&mut self.show_demo_window)
                .show(ctx, |ui| {
                    egui::widgets::global_theme_preference_buttons(ui);
                    ui.separator();
                    ui.heading("Egui Demo Window");
                    ui.label("This is a demo window to show egui capabilities");

                    ui.separator();
                    ui.label("Try different UI widgets:");

                    ui.horizontal(|ui| {
                        ui.label("A slider: ");
                        let mut value = 0.5;
                        ui.add(egui::Slider::new(&mut value, 0.0..=1.0));
                    });

                    ui.checkbox(&mut self.show_settings, "Show Settings Window");
                });
        }

        // Settings window (if enabled)
        if self.show_settings {
            egui::Window::new("Settings")
                .open(&mut self.show_settings)
                .show(ctx, |ui| {
                    ui.heading("Application Settings");

                    ui.separator();
                    ui.label("Background Color:");
                    ui.horizontal(|ui| {
                        ui.label("R:");
                        ui.add(egui::Slider::new(&mut self.background_color[0], 0.0..=1.0));
                    });
                    ui.horizontal(|ui| {
                        ui.label("G:");
                        ui.add(egui::Slider::new(&mut self.background_color[1], 0.0..=1.0));
                    });
                    ui.horizontal(|ui| {
                        ui.label("B:");
                        ui.add(egui::Slider::new(&mut self.background_color[2], 0.0..=1.0));
                    });

                    // Preview color
                    let color_preview = egui::Color32::from_rgb(
                        (self.background_color[0] * 255.0) as u8,
                        (self.background_color[1] * 255.0) as u8,
                        (self.background_color[2] * 255.0) as u8,
                    );

                    ui.horizontal(|ui| {
                        ui.label("Preview:");
                        ui.painter().rect_filled(
                            egui::Rect::from_min_size(
                                ui.cursor().min,
                                egui::vec2(64.0, 24.0),
                            ),
                            egui::CornerRadius::same(4),
                            color_preview,
                        );
                        ui.add_space(70.0); // Make space for the preview
                    });
                });
        }
    }

    /// Renders the top menu bar
    fn render_menu_bar(&mut self, ctx: &Context) {
        egui::TopBottomPanel::top("menu_bar").show(ctx, |ui| {
            egui::menu::bar(ui, |ui| {
                ui.menu_button("File", |ui| {
                    if ui.button("Exit").clicked() {
                        // Signal to exit the application
                        // This would need to be handled by the main app
                    }
                });

                ui.menu_button("View", |ui| {
                    if ui.checkbox(&mut self.show_demo_window, "Demo Window").clicked() {
                        ui.close_menu();
                    }

                    if ui.checkbox(&mut self.show_settings, "Settings").clicked() {
                        ui.close_menu();
                    }

                    if ui.checkbox(&mut self.show_camera_debug, "Camera Debug").clicked() {
                        ui.close_menu();
                    }

                    if ui.checkbox(&mut self.show_world_settings, "World Settings").clicked() {
                        ui.close_menu();
                    }
                });

                ui.menu_button("World", |ui| {
                    if ui.button("Generate New World").clicked() {
                        self.regenerate_world = true;
                        ui.close_menu();
                    }

                    if ui.button("Generate Single Voxel (Debug)").clicked() {
                        self.generate_single_voxel = true;
                        ui.close_menu();
                    }

                    if ui.button("Randomize Seed").clicked() {
                        use std::time::{SystemTime, UNIX_EPOCH};
                        let time = SystemTime::now().duration_since(UNIX_EPOCH).unwrap();
                        self.world_params.seed = time.as_secs() as u32;
                        ui.close_menu();
                    }
                });

                ui.menu_button("Help", |ui| {
                    if ui.button("About").clicked() {
                        // Show about dialog
                        ui.close_menu();
                    }
                });
            });
        });
    }

    /// Returns the current background color
    pub fn background_color(&self) -> [f32; 3] {
        self.background_color
    }

    /// Returns whether camera debug mode is enabled
    pub fn is_camera_debug_enabled(&self) -> bool {
        self.camera_debug_enabled
    }

    /// Returns the current world parameters
    pub fn world_parameters(&self) -> &WorldParameters {
        &self.world_params
    }

    /// Returns whether the world should be regenerated
    pub fn should_regenerate_world(&self) -> bool {
        self.regenerate_world
    }

    /// Resets the regenerate world flag
    pub fn reset_regenerate_flag(&mut self) {
        self.regenerate_world = false;
    }

    /// Returns whether a single voxel world should be generated
    pub fn should_generate_single_voxel(&self) -> bool {
        self.generate_single_voxel
    }

    /// Resets the generate single voxel flag
    pub fn reset_single_voxel_flag(&mut self) {
        self.generate_single_voxel = false;
    }
}
